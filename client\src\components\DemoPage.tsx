import React, { useState, useEffect } from 'react';
import './DemoPage.css';

interface ApiResponse {
  message: string;
  timestamp: string;
}

const DemoPage: React.FC = () => {
  const [count, setCount] = useState(0);
  const [apiData, setApiData] = useState<ApiResponse | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchApiData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/hello');
      const data = await response.json();
      setApiData(data);
    } catch (error) {
      console.error('Error fetching API data:', error);
      setApiData({ message: 'Error fetching data', timestamp: new Date().toISOString() });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApiData();
  }, []);

  return (
    <div className="demo-page">
      <div className="demo-section">
        <h2>Interactive Counter</h2>
        <div className="counter-container">
          <button 
            className="counter-btn" 
            onClick={() => setCount(count - 1)}
            disabled={count <= 0}
          >
            -
          </button>
          <span className="counter-value">{count}</span>
          <button 
            className="counter-btn" 
            onClick={() => setCount(count + 1)}
          >
            +
          </button>
        </div>
        <p className="counter-description">
          This demonstrates React state management and interactivity.
        </p>
      </div>

      <div className="demo-section">
        <h2>API Integration</h2>
        <div className="api-container">
          <button 
            className="api-btn" 
            onClick={fetchApiData}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Fetch from NestJS API'}
          </button>
          {apiData && (
            <div className="api-response">
              <h3>Response from Backend:</h3>
              <p><strong>Message:</strong> {apiData.message}</p>
              <p><strong>Timestamp:</strong> {apiData.timestamp}</p>
            </div>
          )}
        </div>
        <p className="api-description">
          This demonstrates communication between React frontend and NestJS backend.
        </p>
      </div>

      <div className="demo-section">
        <h2>Features Demonstrated</h2>
        <ul className="features-list">
          <li>✅ React components with TypeScript</li>
          <li>✅ State management with hooks</li>
          <li>✅ API calls to NestJS backend</li>
          <li>✅ CSS styling and responsive design</li>
          <li>✅ Webpack bundling and hot reload</li>
          <li>✅ Monolith deployment architecture</li>
        </ul>
      </div>
    </div>
  );
};

export default DemoPage;
