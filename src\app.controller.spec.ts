import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';

describe('AppController', () => {
  let appController: AppController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [AppService],
    }).compile();

    appController = app.get<AppController>(AppController);
  });

  describe('api/hello', () => {
    it('should return an object with message, timestamp, and server', () => {
      const result = appController.getHello();
      expect(result).toHaveProperty('message', 'Hello World!');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('server', 'NestJS');
      expect(typeof result.timestamp).toBe('string');
    });
  });
});
