.demo-page {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.demo-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.demo-section h2 {
  margin: 0 0 1.5rem 0;
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
}

.counter-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 1.5rem 0;
}

.counter-btn {
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 8px;
  width: 50px;
  height: 50px;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.counter-btn:hover:not(:disabled) {
  background: #3182ce;
  transform: translateY(-1px);
}

.counter-btn:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  transform: none;
}

.counter-value {
  font-size: 2rem;
  font-weight: bold;
  color: #2d3748;
  min-width: 60px;
  text-align: center;
}

.counter-description,
.api-description {
  color: #718096;
  font-size: 0.9rem;
  margin-top: 1rem;
  line-height: 1.5;
}

.api-container {
  text-align: center;
}

.api-btn {
  background: #48bb78;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 1.5rem;
}

.api-btn:hover:not(:disabled) {
  background: #38a169;
  transform: translateY(-1px);
}

.api-btn:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  transform: none;
}

.api-response {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: left;
  margin-top: 1rem;
}

.api-response h3 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.1rem;
}

.api-response p {
  margin: 0.5rem 0;
  color: #4a5568;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.features-list li {
  padding: 0.5rem 0;
  color: #4a5568;
  font-size: 1rem;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .demo-page {
    grid-template-columns: 1fr;
  }
  
  .demo-section {
    padding: 1.5rem;
  }
  
  .counter-container {
    gap: 0.5rem;
  }
  
  .counter-btn {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .counter-value {
    font-size: 1.5rem;
    min-width: 50px;
  }
}
