import { useAppStore } from './useAppStore';

// User-related hooks
export const useUser = () => useAppStore((state) => state.user);
export const useIsAuthenticated = () => useAppStore((state) => state.isAuthenticated);
export const useUserActions = () => useAppStore((state) => ({
  setUser: state.setUser,
  login: state.login,
  logout: state.logout,
}));

// UI-related hooks
export const useTheme = () => useAppStore((state) => state.theme);
export const useSidebarOpen = () => useAppStore((state) => state.sidebarOpen);
export const useLoading = () => useAppStore((state) => state.loading);
export const useUIActions = () => useAppStore((state) => ({
  setTheme: state.setTheme,
  toggleSidebar: state.toggleSidebar,
  setLoading: state.setLoading,
}));

// Notification-related hooks
export const useNotifications = () => useAppStore((state) => state.notifications);
export const useUnreadNotifications = () => 
  useAppStore((state) => state.notifications.filter(n => !n.read));
export const useNotificationActions = () => useAppStore((state) => ({
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
  markNotificationAsRead: state.markNotificationAsRead,
  clearAllNotifications: state.clearAllNotifications,
}));

// Combined hooks for convenience
export const useAuth = () => ({
  user: useUser(),
  isAuthenticated: useIsAuthenticated(),
  ...useUserActions(),
});

export const useUI = () => ({
  theme: useTheme(),
  sidebarOpen: useSidebarOpen(),
  loading: useLoading(),
  ...useUIActions(),
});
