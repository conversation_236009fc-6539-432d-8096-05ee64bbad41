import { readFileSync } from 'fs';
import * as yaml from 'js-yaml';
import { join } from 'path';

export interface DatabaseConfig {
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  synchronize: boolean;
  logging: boolean;
  entities: string[];
  migrations: string[];
  subscribers: string[];
  migrationsTableName: string;
  ssl?: boolean;
  dropSchema?: boolean;
}

export interface AppConfig {
  name: string;
  version: string;
  description: string;
  port: number;
  globalPrefix: string;
}

export interface SecurityConfig {
  jwt: {
    secret: string;
    expiresIn: string;
  };
  cors: {
    enabled: boolean;
    origin: string | string[] | boolean;
    credentials: boolean;
  };
}

export interface LoggingConfig {
  level: string;
  format: string;
}

export interface UploadConfig {
  maxFileSize: number;
  allowedMimeTypes: string[];
  destination: string;
}

export interface CacheConfig {
  ttl: number;
  max: number;
}

export interface RateLimitConfig {
  windowMs: number;
  max: number;
}

export interface Configuration {
  app: AppConfig;
  database: DatabaseConfig;
  security: SecurityConfig;
  logging: LoggingConfig;
  upload: UploadConfig;
  cache: CacheConfig;
  rateLimit: RateLimitConfig;
}

const YAML_CONFIG_FILENAME = 'config.yml';

export default (): Configuration => {
  const env = process.env.NODE_ENV || 'development';
  const configPath = join(process.cwd(), YAML_CONFIG_FILENAME);
  const envConfigPath = join(process.cwd(), `config.${env}.yml`);
  
  // Load base configuration
  let config: Configuration;
  try {
    config = yaml.load(readFileSync(configPath, 'utf8')) as Configuration;
  } catch (error) {
    throw new Error(`Failed to load base configuration: ${error.message}`);
  }
  
  // Load environment-specific configuration and merge
  try {
    const envConfig = yaml.load(readFileSync(envConfigPath, 'utf8')) as Partial<Configuration>;
    config = mergeDeep(config, envConfig);
  } catch (error) {
    console.warn(`Environment-specific config not found or invalid: ${envConfigPath}`);
  }
  
  // Replace environment variables in the configuration
  config = replaceEnvVariables(config);
  
  return config;
};

// Deep merge function
function mergeDeep(target: any, source: any): any {
  const output = Object.assign({}, target);
  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target))
          Object.assign(output, { [key]: source[key] });
        else
          output[key] = mergeDeep(target[key], source[key]);
      } else {
        Object.assign(output, { [key]: source[key] });
      }
    });
  }
  return output;
}

function isObject(item: any): boolean {
  return item && typeof item === 'object' && !Array.isArray(item);
}

// Replace environment variables in configuration
function replaceEnvVariables(obj: any): any {
  if (typeof obj === 'string') {
    // Match ${VAR_NAME:default_value} or ${VAR_NAME}
    return obj.replace(/\$\{([^:}]+)(?::([^}]*))?\}/g, (match, varName, defaultValue) => {
      return process.env[varName] || defaultValue || match;
    });
  } else if (Array.isArray(obj)) {
    return obj.map(replaceEnvVariables);
  } else if (typeof obj === 'object' && obj !== null) {
    const result: any = {};
    for (const key in obj) {
      result[key] = replaceEnvVariables(obj[key]);
    }
    return result;
  }
  return obj;
}
