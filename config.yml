# Main application configuration
app:
  name: "NexEd Nest"
  version: "1.0.0"
  description: "NestJS + React Monolithic Application"
  port: 3000
  globalPrefix: "api"
  
# Database configuration
database:
  type: "postgres"
  host: "localhost"
  port: 5432
  username: "postgres"
  password: "password"
  database: "nexed_nest"
  synchronize: true
  logging: false
  entities:
    - "dist/**/*.entity{.ts,.js}"
  migrations:
    - "dist/migrations/*{.ts,.js}"
  subscribers:
    - "dist/subscribers/*{.ts,.js}"
  migrationsTableName: "migrations"
  
# Security configuration
security:
  jwt:
    secret: "your-super-secret-jwt-key-change-in-production"
    expiresIn: "24h"
  cors:
    enabled: true
    origin: true
    credentials: true
    
# Logging configuration
logging:
  level: "info"
  format: "combined"
  
# File upload configuration
upload:
  maxFileSize: 10485760 # 10MB
  allowedMimeTypes:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "application/pdf"
    - "text/plain"
  destination: "./uploads"
  
# Cache configuration
cache:
  ttl: 300 # 5 minutes
  max: 100
  
# Rate limiting
rateLimit:
  windowMs: 900000 # 15 minutes
  max: 100 # limit each IP to 100 requests per windowMs
