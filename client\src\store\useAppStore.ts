import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { AppStore, User, Notification } from './types';

// Initial state
const initialState = {
  // User state
  user: null,
  isAuthenticated: false,
  
  // UI state
  theme: 'light' as const,
  sidebarOpen: false,
  loading: false,
  
  // Notifications
  notifications: [],
};

export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        
        // User actions
        setUser: (user: User | null) => {
          set(
            { 
              user, 
              isAuthenticated: !!user 
            },
            false,
            'setUser'
          );
        },
        
        login: async (email: string, password: string) => {
          set({ loading: true }, false, 'login/start');
          
          try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Mock user data
            const user: User = {
              id: '1',
              name: '<PERSON>',
              email,
              avatar: 'https://via.placeholder.com/40'
            };
            
            get().setUser(user);
            get().addNotification({
              type: 'success',
              title: 'Login Successful',
              message: `Welcome back, ${user.name}!`
            });
          } catch (error) {
            get().addNotification({
              type: 'error',
              title: 'Login Failed',
              message: 'Invalid credentials. Please try again.'
            });
          } finally {
            set({ loading: false }, false, 'login/end');
          }
        },
        
        logout: () => {
          set(
            { 
              user: null, 
              isAuthenticated: false 
            },
            false,
            'logout'
          );
          get().addNotification({
            type: 'info',
            title: 'Logged Out',
            message: 'You have been successfully logged out.'
          });
        },
        
        // UI actions
        setTheme: (theme: 'light' | 'dark') => {
          set({ theme }, false, 'setTheme');
        },
        
        toggleSidebar: () => {
          set(
            (state) => ({ sidebarOpen: !state.sidebarOpen }),
            false,
            'toggleSidebar'
          );
        },
        
        setLoading: (loading: boolean) => {
          set({ loading }, false, 'setLoading');
        },
        
        // Notification actions
        addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
          const newNotification: Notification = {
            ...notification,
            id: Date.now().toString(),
            timestamp: new Date(),
            read: false,
          };
          
          set(
            (state) => ({
              notifications: [newNotification, ...state.notifications],
            }),
            false,
            'addNotification'
          );
        },
        
        removeNotification: (id: string) => {
          set(
            (state) => ({
              notifications: state.notifications.filter(n => n.id !== id),
            }),
            false,
            'removeNotification'
          );
        },
        
        markNotificationAsRead: (id: string) => {
          set(
            (state) => ({
              notifications: state.notifications.map(n =>
                n.id === id ? { ...n, read: true } : n
              ),
            }),
            false,
            'markNotificationAsRead'
          );
        },
        
        clearAllNotifications: () => {
          set({ notifications: [] }, false, 'clearAllNotifications');
        },
      }),
      {
        name: 'app-store',
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
          theme: state.theme,
        }),
      }
    ),
    {
      name: 'AppStore',
    }
  )
);
