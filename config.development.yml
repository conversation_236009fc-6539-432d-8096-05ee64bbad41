# Development environment configuration
app:
  port: 3000
  
# Database configuration for development
database:
  host: "localhost"
  port: 5432
  username: "postgres"
  password: "password"
  database: "nexed_nest_dev"
  synchronize: true
  logging: true
  dropSchema: false
  
# Security configuration for development
security:
  jwt:
    secret: "dev-jwt-secret-key"
    expiresIn: "7d"
  cors:
    enabled: true
    origin: ["http://localhost:3000", "http://localhost:8080"]
    credentials: true
    
# Logging configuration for development
logging:
  level: "debug"
  format: "dev"
  
# Cache configuration for development
cache:
  ttl: 60 # 1 minute for faster development
  max: 50
  
# Rate limiting for development (more lenient)
rateLimit:
  windowMs: 60000 # 1 minute
  max: 1000 # much higher limit for development
