import { Controller, Get, Res } from '@nestjs/common';
import { AppService } from './app.service';
import { Response } from 'express';
import { join } from 'path';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getRoot(@Res() res: Response) {
    res.sendFile(join(__dirname, '..', 'dist', 'client', 'index.html'));
  }

  @Get('api/hello')
  getHello() {
    return {
      message: this.appService.getHello(),
      timestamp: new Date().toISOString(),
      server: 'NestJS',
    };
  }

  // Catch-all route for React Router (SPA)
  @Get('*')
  getSPA(@Res() res: Response) {
    res.sendFile(join(__dirname, '..', 'dist', 'client', 'index.html'));
  }
}
