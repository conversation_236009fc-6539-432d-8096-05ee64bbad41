# Production environment configuration
app:
  port: ${PORT:3000}
  
# Database configuration for production
database:
  host: ${DB_HOST:localhost}
  port: ${DB_PORT:5432}
  username: ${DB_USERNAME:postgres}
  password: ${DB_PASSWORD}
  database: ${DB_NAME:nexed_nest_prod}
  synchronize: false
  logging: false
  ssl: ${DB_SSL:false}
  
# Security configuration for production
security:
  jwt:
    secret: ${JWT_SECRET}
    expiresIn: "1h"
  cors:
    enabled: true
    origin: ${CORS_ORIGIN}
    credentials: true
    
# Logging configuration for production
logging:
  level: "warn"
  format: "combined"
  
# Cache configuration for production
cache:
  ttl: 600 # 10 minutes
  max: 1000
  
# Rate limiting for production
rateLimit:
  windowMs: 900000 # 15 minutes
  max: 100
